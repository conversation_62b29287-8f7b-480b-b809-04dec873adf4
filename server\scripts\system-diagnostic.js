#!/usr/bin/env node

/**
 * StreamDB System Diagnostic Tool
 * Comprehensive system audit and troubleshooting for production environment
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const execAsync = promisify(exec);

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const db = require('../config/database');

class SystemDiagnostic {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      tests: [],
      summary: {
        passed: 0,
        failed: 0,
        warnings: 0
      }
    };
  }

  log(level, test, message, details = {}) {
    const result = {
      level,
      test,
      message,
      details,
      timestamp: new Date().toISOString()
    };

    this.results.tests.push(result);
    this.results.summary[level === 'PASS' ? 'passed' : level === 'FAIL' ? 'failed' : 'warnings']++;

    const colors = {
      PASS: '\x1b[32m✅',
      FAIL: '\x1b[31m❌',
      WARN: '\x1b[33m⚠️',
      INFO: '\x1b[34mℹ️'
    };

    console.log(`${colors[level] || colors.INFO} ${test}: ${message}\x1b[0m`);
    if (Object.keys(details).length > 0) {
      console.log(`   Details:`, details);
    }
  }

  async testDatabaseConnection() {
    try {
      if (!db || !db.query) {
        this.log('FAIL', 'Database Connection', 'Database module not available');
        return false;
      }

      await new Promise((resolve, reject) => {
        db.query('SELECT 1 as test', (err, results) => {
          if (err) reject(err);
          else resolve(results);
        });
      });

      this.log('PASS', 'Database Connection', 'Successfully connected to MySQL database');
      return true;
    } catch (error) {
      this.log('FAIL', 'Database Connection', 'Failed to connect to database', {
        error: error.message,
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME
      });
      return false;
    }
  }

  async testWebhookConfiguration() {
    const webhookSecret = process.env.WEBHOOK_SECRET;
    const deployScript = process.env.DEPLOY_SCRIPT;

    if (!webhookSecret) {
      this.log('FAIL', 'Webhook Configuration', 'WEBHOOK_SECRET not configured');
      return false;
    }

    if (webhookSecret.length < 32) {
      this.log('WARN', 'Webhook Configuration', 'WEBHOOK_SECRET should be at least 32 characters');
    } else {
      this.log('PASS', 'Webhook Configuration', 'WEBHOOK_SECRET properly configured');
    }

    if (!deployScript || !fs.existsSync(deployScript)) {
      this.log('FAIL', 'Deployment Script', 'Deploy script not found', { path: deployScript });
      return false;
    }

    try {
      const stats = fs.statSync(deployScript);
      if (!(stats.mode & parseInt('100', 8))) {
        this.log('WARN', 'Deployment Script', 'Script not executable, attempting to fix');
        fs.chmodSync(deployScript, '755');
      }
      this.log('PASS', 'Deployment Script', 'Deploy script exists and is executable');
    } catch (error) {
      this.log('FAIL', 'Deployment Script', 'Cannot access deploy script', { error: error.message });
      return false;
    }

    return true;
  }

  async testGitRepository() {
    try {
      const projectDir = path.join(__dirname, '../..');
      const { stdout: branch } = await execAsync('git branch --show-current', { cwd: projectDir });
      const { stdout: status } = await execAsync('git status --porcelain', { cwd: projectDir });
      const { stdout: lastCommit } = await execAsync('git log -1 --oneline', { cwd: projectDir });

      this.log('PASS', 'Git Repository', 'Git repository accessible', {
        currentBranch: branch.trim(),
        hasUncommittedChanges: status.trim().length > 0,
        lastCommit: lastCommit.trim()
      });

      // Test GitHub connectivity
      await execAsync('git fetch --dry-run origin', { cwd: projectDir, timeout: 10000 });
      this.log('PASS', 'GitHub Connectivity', 'Can connect to GitHub repository');

      return true;
    } catch (error) {
      this.log('FAIL', 'Git Repository', 'Git repository issues', { error: error.message });
      return false;
    }
  }

  async testPM2Processes() {
    try {
      const { stdout } = await execAsync('pm2 jlist');
      const processes = JSON.parse(stdout);
      
      const streamdbProcess = processes.find(p => p.name === 'streamdb-online' || p.name === 'index');
      const webhookProcess = processes.find(p => p.name === 'webhook-server' || p.name === 'github-webhook');

      if (streamdbProcess) {
        this.log('PASS', 'PM2 Main Process', `Main application running (${streamdbProcess.pm2_env.status})`, {
          pid: streamdbProcess.pid,
          uptime: streamdbProcess.pm2_env.pm_uptime,
          restarts: streamdbProcess.pm2_env.restart_time
        });
      } else {
        this.log('FAIL', 'PM2 Main Process', 'Main application not running in PM2');
      }

      if (webhookProcess) {
        this.log('PASS', 'PM2 Webhook Process', `Webhook server running (${webhookProcess.pm2_env.status})`, {
          pid: webhookProcess.pid,
          port: webhookProcess.pm2_env.WEBHOOK_PORT || 9000
        });
      } else {
        this.log('WARN', 'PM2 Webhook Process', 'Webhook server not running as separate PM2 process');
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'PM2 Processes', 'Cannot check PM2 processes', { error: error.message });
      return false;
    }
  }

  async testNginxConfiguration() {
    try {
      // Test if nginx is running
      await execAsync('systemctl is-active nginx');
      this.log('PASS', 'Nginx Service', 'Nginx is running');

      // Test nginx configuration
      await execAsync('nginx -t');
      this.log('PASS', 'Nginx Configuration', 'Nginx configuration is valid');

      return true;
    } catch (error) {
      this.log('FAIL', 'Nginx', 'Nginx issues detected', { error: error.message });
      return false;
    }
  }

  async testAPIEndpoints() {
    const endpoints = [
      '/api/health',
      '/api/webhook/health'
    ];

    for (const endpoint of endpoints) {
      try {
        const url = `http://localhost:${process.env.PORT || 3001}${endpoint}`;
        const response = await fetch(url);

        if (response.ok) {
          this.log('PASS', 'API Endpoint', `${endpoint} responding correctly`);
        } else {
          this.log('FAIL', 'API Endpoint', `${endpoint} returned ${response.status}`);
        }
      } catch (error) {
        this.log('FAIL', 'API Endpoint', `${endpoint} not accessible`, { error: error.message });
      }
    }
  }

  async testReverseProxySetup() {
    try {
      // Test if we can determine the actual client IP
      const testIP = '*************'; // Expected proxy IP
      
      this.log('INFO', 'Reverse Proxy', 'Testing proxy configuration', {
        expectedProxyIP: testIP,
        currentServerIP: '***********',
        note: 'Manual verification required for full proxy chain test'
      });

      // Check if we're behind a proxy by looking for proxy headers
      const hasProxyHeaders = process.env.FRONTEND_URL?.includes('https://');
      
      if (hasProxyHeaders) {
        this.log('PASS', 'Reverse Proxy', 'Application configured for proxy environment');
      } else {
        this.log('WARN', 'Reverse Proxy', 'Application may not be properly configured for proxy');
      }

      return true;
    } catch (error) {
      this.log('FAIL', 'Reverse Proxy', 'Proxy configuration test failed', { error: error.message });
      return false;
    }
  }

  async runAllTests() {
    console.log('\n🔍 StreamDB System Diagnostic Starting...\n');

    await this.testDatabaseConnection();
    await this.testWebhookConfiguration();
    await this.testGitRepository();
    await this.testPM2Processes();
    await this.testNginxConfiguration();
    await this.testAPIEndpoints();
    await this.testReverseProxySetup();

    console.log('\n📊 Diagnostic Summary:');
    console.log(`✅ Passed: ${this.results.summary.passed}`);
    console.log(`❌ Failed: ${this.results.summary.failed}`);
    console.log(`⚠️  Warnings: ${this.results.summary.warnings}`);

    // Save results to file
    const resultsFile = path.join(__dirname, '../logs/diagnostic-results.json');
    fs.writeFileSync(resultsFile, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Full results saved to: ${resultsFile}`);

    return this.results;
  }
}

// Run diagnostic if called directly
if (require.main === module) {
  const diagnostic = new SystemDiagnostic();
  diagnostic.runAllTests().catch(console.error);
}

module.exports = SystemDiagnostic;
