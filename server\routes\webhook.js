const express = require('express');
const crypto = require('crypto');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const db = require('../config/database');
const {
  webhookSecurityStack,
  adminWebhookSecurityStack,
  deploymentSecurityStack,
  logSecurityEvent
} = require('../middleware/webhookSecurity');

const router = express.Router();

// Configuration
const WEBHOOK_PORT = process.env.WEBHOOK_PORT || 9000;
const WEBHOOK_HOST = '0.0.0.0'; // Listen on all interfaces for GitHub
const DEPLOY_SCRIPT = process.env.DEPLOY_SCRIPT || path.join(__dirname, '../../deployment/deploy.sh');
const ALLOWED_BRANCH = process.env.DEPLOY_BRANCH || 'New-Main-1';
const MAX_DEPLOYMENTS_PER_HOUR = 10;

// Rate limiting storage (in production, use Redis or database)
const deploymentHistory = [];

// Utility functions
function log(level, message, data = {}) {
  const timestamp = new Date().toISOString();
  const logEntry = {
    timestamp,
    level,
    message,
    ...data
  };
  
  console.log(`[${timestamp}] ${level}: ${message}`, data);
  
  // Store in database
  if (db && db.query) {
    const logData = JSON.stringify(logEntry);
    db.query(
      'INSERT INTO deployment_logs (level, message, data, created_at) VALUES (?, ?, ?, NOW())',
      [level, message, logData],
      (err) => {
        if (err) console.error('Failed to store deployment log:', err);
      }
    );
  }
}

function verifySignature(payload, signature) {
  if (!WEBHOOK_SECRET || !signature) {
    return false;
  }
  
  const expectedSignature = 'sha256=' + crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(payload, 'utf8')
    .digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature),
    Buffer.from(expectedSignature)
  );
}

function isRateLimited() {
  const oneHourAgo = Date.now() - (60 * 60 * 1000);
  const recentDeployments = deploymentHistory.filter(time => time > oneHourAgo);
  return recentDeployments.length >= MAX_DEPLOYMENTS_PER_HOUR;
}

function recordDeployment() {
  deploymentHistory.push(Date.now());
  // Keep only last 24 hours of data
  const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
  const index = deploymentHistory.findIndex(time => time > oneDayAgo);
  if (index > 0) {
    deploymentHistory.splice(0, index);
  }
}

function validatePayload(payload) {
  try {
    const data = JSON.parse(payload);
    
    // Check if it's a push event
    if (!data.ref) {
      return { valid: false, reason: 'Not a push event' };
    }
    
    // Check branch
    const branch = data.ref.replace('refs/heads/', '');
    if (branch !== ALLOWED_BRANCH) {
      return { 
        valid: false, 
        reason: `Push to '${branch}' branch, only '${ALLOWED_BRANCH}' triggers deployment` 
      };
    }
    
    // Check if there are actual commits
    if (!data.commits || data.commits.length === 0) {
      return { valid: false, reason: 'No commits in push' };
    }
    
    return { 
      valid: true, 
      data: {
        branch,
        commits: data.commits.length,
        pusher: data.pusher?.name || 'unknown',
        repository: data.repository?.full_name || 'unknown',
        commitMessages: data.commits.map(c => c.message).slice(0, 3)
      }
    };
  } catch (error) {
    return { valid: false, reason: 'Invalid JSON payload' };
  }
}

function executeDeploy() {
  return new Promise((resolve, reject) => {
    log('INFO', 'Starting deployment execution');
    
    const deployCommand = `${DEPLOY_SCRIPT} deploy`;
    
    exec(deployCommand, { 
      timeout: 300000, // 5 minutes timeout
      cwd: path.dirname(DEPLOY_SCRIPT)
    }, (error, stdout, stderr) => {
      if (error) {
        log('ERROR', 'Deployment failed', {
          error: error.message,
          code: error.code,
          stderr: stderr
        });
        reject(error);
      } else {
        log('SUCCESS', 'Deployment completed successfully', {
          stdout: stdout.trim()
        });
        resolve(stdout);
      }
    });
  });
}

// Middleware for raw body parsing (needed for signature verification)
router.use('/github', express.raw({ type: 'application/json' }));

// GitHub webhook endpoint with comprehensive security
router.post('/github', webhookSecurityStack, async (req, res) => {
  try {
    const clientIP = req.headers['x-forwarded-for'] || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'] || 'unknown';
    const body = req.body.toString();
    
    log('INFO', 'Webhook request received', {
      ip: clientIP,
      userAgent: userAgent.substring(0, 100),
      contentLength: body.length
    });
    
    // Verify signature
    const signature = req.headers['x-hub-signature-256'];
    if (!verifySignature(body, signature)) {
      log('ERROR', 'Invalid webhook signature', { ip: clientIP });
      return res.status(401).json({ 
        error: 'Unauthorized',
        message: 'Invalid signature'
      });
    }
    
    // Validate payload
    const validation = validatePayload(body);
    if (!validation.valid) {
      log('INFO', 'Webhook ignored', { reason: validation.reason, ip: clientIP });
      return res.status(200).json({ 
        message: 'Webhook received but ignored',
        reason: validation.reason
      });
    }
    
    // Check rate limiting
    if (isRateLimited()) {
      log('WARNING', 'Deployment rate limited', { ip: clientIP });
      return res.status(429).json({ 
        error: 'Too Many Requests',
        message: 'Deployment rate limit exceeded'
      });
    }
    
    // Record deployment attempt
    recordDeployment();
    
    log('INFO', 'Starting deployment', {
      ...validation.data,
      ip: clientIP
    });
    
    // Store deployment record in database
    if (db && db.query) {
      db.query(
        'INSERT INTO deployments (status, branch, commits, pusher, repository, ip_address, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())',
        ['STARTED', validation.data.branch, validation.data.commits, validation.data.pusher, validation.data.repository, clientIP],
        (err, result) => {
          if (err) {
            console.error('Failed to store deployment record:', err);
          }
        }
      );
    }
    
    // Execute deployment
    try {
      await executeDeploy();
      
      // Update deployment status
      if (db && db.query) {
        db.query(
          'UPDATE deployments SET status = ?, completed_at = NOW() WHERE status = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) ORDER BY created_at DESC LIMIT 1',
          ['SUCCESS', 'STARTED'],
          (err) => {
            if (err) console.error('Failed to update deployment status:', err);
          }
        );
      }
      
      res.status(200).json({ 
        message: 'Deployment successful',
        timestamp: new Date().toISOString(),
        commits: validation.data.commits
      });
      
    } catch (error) {
      log('ERROR', 'Deployment execution failed', {
        error: error.message,
        ip: clientIP
      });
      
      // Update deployment status
      if (db && db.query) {
        db.query(
          'UPDATE deployments SET status = ?, error_message = ?, completed_at = NOW() WHERE status = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) ORDER BY created_at DESC LIMIT 1',
          ['FAILED', error.message, 'STARTED'],
          (err) => {
            if (err) console.error('Failed to update deployment status:', err);
          }
        );
      }
      
      res.status(500).json({ 
        error: 'Deployment Failed',
        message: 'Deployment execution failed'
      });
    }
    
  } catch (error) {
    log('ERROR', 'Webhook processing error', {
      error: error.message,
      stack: error.stack
    });
    
    res.status(500).json({ 
      error: 'Internal Server Error',
      message: 'Webhook processing failed'
    });
  }
});

// Admin panel endpoints for deployment management
router.get('/status', adminWebhookSecurityStack, async (req, res) => {
  try {
    if (!db || !db.query) {
      return res.status(503).json({ error: 'Database not available' });
    }

    // Get recent deployments
    const [deployments] = await db.promise().query(`
      SELECT * FROM recent_deployments LIMIT 10
    `);

    // Get deployment statistics
    const [stats] = await db.promise().query(`
      SELECT * FROM deployment_stats
    `);

    // Get recent logs
    const [logs] = await db.promise().query(`
      SELECT * FROM recent_logs LIMIT 20
    `);

    // Check if webhook is accessible
    const webhookUrl = `${req.protocol}://${req.get('host')}/api/webhook/github`;

    res.json({
      status: 'OK',
      webhook_url: webhookUrl,
      deployments: deployments || [],
      statistics: stats[0] || {},
      recent_logs: logs || [],
      configuration: {
        allowed_branch: ALLOWED_BRANCH,
        max_deployments_per_hour: MAX_DEPLOYMENTS_PER_HOUR,
        webhook_secret_configured: !!WEBHOOK_SECRET
      }
    });

  } catch (error) {
    console.error('Error fetching deployment status:', error);
    res.status(500).json({
      error: 'Failed to fetch deployment status',
      message: error.message
    });
  }
});

// Test webhook connectivity
router.get('/test', adminWebhookSecurityStack, async (req, res) => {
  try {
    const webhookUrl = `${req.protocol}://${req.get('host')}/api/webhook/github`;

    // Test if deployment script exists
    const scriptExists = fs.existsSync(DEPLOY_SCRIPT);

    // Test database connectivity
    let dbConnected = false;
    if (db && db.query) {
      try {
        await db.promise().query('SELECT 1');
        dbConnected = true;
      } catch (err) {
        console.error('Database test failed:', err);
      }
    }

    res.json({
      webhook_url: webhookUrl,
      webhook_secret_configured: !!WEBHOOK_SECRET,
      deploy_script_exists: scriptExists,
      deploy_script_path: DEPLOY_SCRIPT,
      database_connected: dbConnected,
      allowed_branch: ALLOWED_BRANCH,
      rate_limit: `${MAX_DEPLOYMENTS_PER_HOUR}/hour`,
      test_timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error testing webhook:', error);
    res.status(500).json({
      error: 'Webhook test failed',
      message: error.message
    });
  }
});

// Manual deployment trigger
router.post('/deploy', deploymentSecurityStack, async (req, res) => {
  try {
    const { branch = ALLOWED_BRANCH, force = false } = req.body;

    // Check rate limiting unless forced
    if (!force && isRateLimited()) {
      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: 'Too many deployments in the last hour'
      });
    }

    // Record manual deployment
    recordDeployment();

    log('INFO', 'Manual deployment triggered', {
      branch,
      force,
      user: req.user?.username || 'unknown'
    });

    // Store manual deployment record
    if (db && db.query) {
      db.query(
        'INSERT INTO manual_deployments (admin_user_id, deployment_type, target_branch, status) VALUES (?, ?, ?, ?)',
        [req.user?.id || null, 'MANUAL', branch, 'RUNNING'],
        (err) => {
          if (err) console.error('Failed to store manual deployment record:', err);
        }
      );
    }

    // Execute deployment
    try {
      const result = await executeDeploy();

      res.json({
        message: 'Manual deployment completed successfully',
        timestamp: new Date().toISOString(),
        branch,
        output: result
      });

    } catch (error) {
      log('ERROR', 'Manual deployment failed', {
        error: error.message,
        branch,
        user: req.user?.username || 'unknown'
      });

      res.status(500).json({
        error: 'Manual deployment failed',
        message: error.message
      });
    }

  } catch (error) {
    console.error('Error triggering manual deployment:', error);
    res.status(500).json({
      error: 'Failed to trigger deployment',
      message: error.message
    });
  }
});

// Get deployment logs
router.get('/logs', adminWebhookSecurityStack, async (req, res) => {
  try {
    const { limit = 50, level, since } = req.query;

    if (!db || !db.query) {
      return res.status(503).json({ error: 'Database not available' });
    }

    let query = 'SELECT * FROM deployment_logs';
    const params = [];
    const conditions = [];

    if (level) {
      conditions.push('level = ?');
      params.push(level);
    }

    if (since) {
      conditions.push('created_at >= ?');
      params.push(since);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' ORDER BY created_at DESC LIMIT ?';
    params.push(parseInt(limit));

    const [logs] = await db.promise().query(query, params);

    res.json({
      logs: logs || [],
      total: logs?.length || 0,
      filters: { level, since, limit }
    });

  } catch (error) {
    console.error('Error fetching deployment logs:', error);
    res.status(500).json({
      error: 'Failed to fetch logs',
      message: error.message
    });
  }
});

module.exports = router;
